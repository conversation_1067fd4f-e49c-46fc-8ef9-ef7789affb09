import { db } from '../firebase';
import {
  collection,
  addDoc,
  query,
  where,
  orderBy,
  onSnapshot,
  doc,
  updateDoc,
  getDoc,
  getDocs,
  setDoc,
  writeBatch,
  serverTimestamp,
  arrayUnion,
  arrayRemove
} from 'firebase/firestore';

/**
 * Private Messaging Service
 * Handles secure messaging between users with encryption and real-time updates
 */

// Simple encryption for message content (in production, use proper encryption)
const encryptMessage = (message) => {
  return btoa(unescape(encodeURIComponent(message)));
};

const decryptMessage = (encryptedMessage) => {
  try {
    return decodeURIComponent(escape(atob(encryptedMessage)));
  } catch (error) {
    console.error('Error decrypting message:', error);
    return '[Message could not be decrypted]';
  }
};

/**
 * Send a private message to another user
 */
export async function sendMessage(senderId, receiverId, content, messageType = 'text') {
  try {
    const encryptedContent = encryptMessage(content);
    
    // Create conversation ID (consistent regardless of who sends first)
    const conversationId = [senderId, receiverId].sort().join('_');
    
    const messageData = {
      conversationId,
      senderId,
      receiverId,
      content: encryptedContent,
      messageType, // 'text', 'image', 'file'
      timestamp: serverTimestamp(),
      read: false,
      edited: false,
      deleted: false
    };

    const messageRef = await addDoc(collection(db, 'messages'), messageData);

    // Update conversation metadata
    await updateConversationMetadata(conversationId, senderId, receiverId, content);

    return {
      success: true,
      messageId: messageRef.id,
      message: 'Message sent successfully'
    };
  } catch (error) {
    console.error('Error sending message:', error);
    throw new Error('Failed to send message');
  }
}

/**
 * Update conversation metadata for quick access
 */
async function updateConversationMetadata(conversationId, senderId, receiverId, lastMessage) {
  try {
    const conversationRef = doc(db, 'conversations', conversationId);
    const conversationDoc = await getDoc(conversationRef);

    const conversationData = {
      id: conversationId,
      participants: [senderId, receiverId],
      lastMessage: lastMessage.substring(0, 100), // Store first 100 chars for preview
      lastMessageTime: serverTimestamp(),
      lastMessageSender: senderId,
      updatedAt: serverTimestamp()
    };

    if (conversationDoc.exists()) {
      await updateDoc(conversationRef, conversationData);
    } else {
      // Create new conversation
      await setDoc(conversationRef, {
        ...conversationData,
        createdAt: serverTimestamp()
      });
    }
  } catch (error) {
    console.error('Error updating conversation metadata:', error);
  }
}

/**
 * Get messages for a conversation
 */
export function getMessages(conversationId, callback) {
  try {
    const messagesQuery = query(
      collection(db, 'messages'),
      where('conversationId', '==', conversationId),
      orderBy('timestamp', 'asc')
    );

    return onSnapshot(messagesQuery, (snapshot) => {
      const messages = snapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          content: decryptMessage(data.content), // Decrypt for display
          timestamp: data.timestamp?.toDate() || new Date()
        };
      });
      
      callback(messages);
    });
  } catch (error) {
    console.error('Error getting messages:', error);
    throw error;
  }
}

/**
 * Get user's conversations
 */
export function getUserConversations(userId, callback) {
  try {
    const conversationsQuery = query(
      collection(db, 'conversations'),
      where('participants', 'array-contains', userId),
      orderBy('lastMessageTime', 'desc')
    );

    return onSnapshot(conversationsQuery, (snapshot) => {
      const conversations = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        lastMessageTime: doc.data().lastMessageTime?.toDate() || new Date()
      }));

      callback(conversations);
    }, (error) => {
      console.error('Error in conversations listener:', error);
      // Return empty array on error to prevent infinite loading
      callback([]);
    });
  } catch (error) {
    console.error('Error getting conversations:', error);
    // Return empty array on error
    callback([]);
    return () => {}; // Return empty unsubscribe function
  }
}

/**
 * Mark messages as read
 */
export async function markMessagesAsRead(conversationId, userId) {
  try {
    const messagesQuery = query(
      collection(db, 'messages'),
      where('conversationId', '==', conversationId),
      where('receiverId', '==', userId),
      where('read', '==', false)
    );

    const snapshot = await getDocs(messagesQuery);
    const batch = writeBatch(db);

    snapshot.docs.forEach(doc => {
      batch.update(doc.ref, { read: true, readAt: serverTimestamp() });
    });

    await batch.commit();

    return {
      success: true,
      markedCount: snapshot.docs.length
    };
  } catch (error) {
    console.error('Error marking messages as read:', error);
    throw error;
  }
}

/**
 * Delete a message
 */
export async function deleteMessage(messageId, userId) {
  try {
    const messageRef = doc(db, 'messages', messageId);
    const messageDoc = await getDoc(messageRef);

    if (!messageDoc.exists()) {
      throw new Error('Message not found');
    }

    const messageData = messageDoc.data();
    
    // Only sender can delete their own messages
    if (messageData.senderId !== userId) {
      throw new Error('You can only delete your own messages');
    }

    await updateDoc(messageRef, {
      deleted: true,
      deletedAt: serverTimestamp(),
      content: encryptMessage('[Message deleted]')
    });

    return {
      success: true,
      message: 'Message deleted successfully'
    };
  } catch (error) {
    console.error('Error deleting message:', error);
    throw error;
  }
}

/**
 * Edit a message
 */
export async function editMessage(messageId, userId, newContent) {
  try {
    const messageRef = doc(db, 'messages', messageId);
    const messageDoc = await getDoc(messageRef);

    if (!messageDoc.exists()) {
      throw new Error('Message not found');
    }

    const messageData = messageDoc.data();
    
    // Only sender can edit their own messages
    if (messageData.senderId !== userId) {
      throw new Error('You can only edit your own messages');
    }

    // Don't allow editing messages older than 24 hours
    const messageTime = messageData.timestamp?.toDate() || new Date(0);
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    
    if (messageTime < twentyFourHoursAgo) {
      throw new Error('Messages can only be edited within 24 hours');
    }

    await updateDoc(messageRef, {
      content: encryptMessage(newContent),
      edited: true,
      editedAt: serverTimestamp()
    });

    return {
      success: true,
      message: 'Message edited successfully'
    };
  } catch (error) {
    console.error('Error editing message:', error);
    throw error;
  }
}

/**
 * Search messages in conversations
 */
export async function searchMessages(userId, searchTerm) {
  try {
    // Get all conversations for the user
    const conversationsQuery = query(
      collection(db, 'conversations'),
      where('participants', 'array-contains', userId)
    );

    const conversationsSnapshot = await getDocs(conversationsQuery);
    const conversationIds = conversationsSnapshot.docs.map(doc => doc.id);

    // Search messages in all user's conversations
    const searchResults = [];
    
    for (const conversationId of conversationIds) {
      const messagesQuery = query(
        collection(db, 'messages'),
        where('conversationId', '==', conversationId),
        orderBy('timestamp', 'desc')
      );

      const messagesSnapshot = await getDocs(messagesQuery);
      
      messagesSnapshot.docs.forEach(doc => {
        const data = doc.data();
        const decryptedContent = decryptMessage(data.content);
        
        if (decryptedContent.toLowerCase().includes(searchTerm.toLowerCase()) && !data.deleted) {
          searchResults.push({
            id: doc.id,
            ...data,
            content: decryptedContent,
            timestamp: data.timestamp?.toDate() || new Date()
          });
        }
      });
    }

    // Sort by timestamp (newest first)
    searchResults.sort((a, b) => b.timestamp - a.timestamp);

    return searchResults;
  } catch (error) {
    console.error('Error searching messages:', error);
    throw error;
  }
}

/**
 * Get unread message count for user
 */
export function getUnreadMessageCount(userId, callback) {
  try {
    const unreadQuery = query(
      collection(db, 'messages'),
      where('receiverId', '==', userId),
      where('read', '==', false)
    );

    return onSnapshot(unreadQuery, (snapshot) => {
      callback(snapshot.docs.length);
    }, (error) => {
      console.error('Error in unread count listener:', error);
      // Return 0 on error
      callback(0);
    });
  } catch (error) {
    console.error('Error getting unread count:', error);
    // Return 0 on error
    callback(0);
    return () => {}; // Return empty unsubscribe function
  }
}

/**
 * Block/unblock a user
 */
export async function toggleBlockUser(userId, targetUserId, block = true) {
  try {
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      throw new Error('User not found');
    }

    const userData = userDoc.data();
    const blockedUsers = userData.blockedUsers || [];

    if (block) {
      if (!blockedUsers.includes(targetUserId)) {
        await updateDoc(userRef, {
          blockedUsers: arrayUnion(targetUserId)
        });
      }
    } else {
      await updateDoc(userRef, {
        blockedUsers: arrayRemove(targetUserId)
      });
    }

    return {
      success: true,
      message: block ? 'User blocked successfully' : 'User unblocked successfully'
    };
  } catch (error) {
    console.error('Error toggling block user:', error);
    throw error;
  }
}

/**
 * Check if user is blocked
 */
export async function isUserBlocked(userId, targetUserId) {
  try {
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      return false;
    }

    const userData = userDoc.data();
    const blockedUsers = userData.blockedUsers || [];

    return blockedUsers.includes(targetUserId);
  } catch (error) {
    console.error('Error checking if user is blocked:', error);
    return false;
  }
}
